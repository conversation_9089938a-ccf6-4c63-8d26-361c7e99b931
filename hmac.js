// FOR SERVER TO SERVER.

// VPS Side function on Deno.
import { createHmac } from "node:crypto"; // Deno supports "node:****" imports.

//const SHARED_SECRET = Deno.env.get("SHARED_SECRET"); // Store securely!
const SHARED_SECRET = 'Sfd789473shjml87Chg7kd78kdc7434hggfkfxxcRET'; // Store securely!

export async function getRefreshToken() {
  let t2 = performance.now();
  // 1. Generate auth headers.
  const timestamp = `${Date.now()}`;
  let t0 =  performance.now();
  const signature = createHmac("sha256", SHARED_SECRET).update(timestamp).digest("hex");
  let t1 = performance.now();
  console.log("HMAC Time: ", (t1 - t0).toFixed(2), 'ms');
  // 2. Make request
  try {
    const res = await fetch("https://2demo.vercel.app/auth", {
      headers: {
        "X-Auth-Timestamp": timestamp,
        "X-Auth-Signature": signature,
        "X-Auth-Client-ID": "nrp-aws-vps", // Unique identifier.
      },
    });
    const data = await res.json();
    console.log('Vercel Response: ', data);
    let t3 = performance.now();
    console.log("Total Time: ", (t3 - t2).toFixed(2), 'ms');
  } catch (err) {
    console.error("Auth failed:", err.message);
    throw err;
  }
};


// Vercel Side function to verify the request.
import { /* createHmac, */ timingSafeEqual } from "node:crypto";

export { hmac };

async function hmac(ctx) {
    // 1. Extract headers
    const timestamp = ctx.req.header("x-auth-timestamp");
    const signature = ctx.req.header("x-auth-signature");
    const clientId = ctx.req.header("x-auth-client-id");

    if (!timestamp || !signature || !clientId) {
        ctx.status(401);
        return ctx.json({ error: "Missing auth headers." });
    }

    // 2. Verify timestamp (60-second window).
    if (Math.abs(Date.now() - Number(timestamp)) > 60000) {
        ctx.status(401);
        return ctx.json({ error: "Expired ctx.request." });
    }

    // 3. Verify HMAC.
    // const expectedSig = createHmac("sha256", process.env.SHARED_SECRET).update(timestamp).digest("hex");
    const expectedSig = createHmac("sha256", 'Sfd789473shjml87Chg7kd78kdc7434hggfkfxxcRET').update(timestamp).digest("hex");
    
    // Use timingSafeEqual (ensure buffers are equal length)
    const signatureBuf = Buffer.from(signature);
    const expectedSigBuf = Buffer.from(expectedSig);

    if (signatureBuf.length !== expectedSigBuf.length) {
        ctx.res.status(401);
        return ctx.json({ error: "Invalid signature length." });
    }
    if (!timingSafeEqual(signatureBuf, expectedSigBuf)) {
        ctx.res.status(401);
        return ctx.json({ error: "Invalid signature." });
    }

    // 4. Verify client ID (optional).
    if (clientId !== "nrp-aws-vps") {
        ctx.res.status(403);
        return ctx.json({ error: "Unauthorized client." });
    }

    return ctx.json({ message: 'Success! The credentials are valid.'});
    
}







// FOR WEBPAGES TO SERVER.

// Web Authentication function with 7-day expiry
// import { createHmac, timingSafeEqual } from "node:crypto";

// Generate auth token for web pages (client-side)
export function generateWebAuthToken(userId) {
  const SHARED_SECRET = 'Sfd789473shjml87Chg7kd78kdc7434hggfkfxxcRET'; // Store securely!
  const timestamp = Date.now();
  const expiryTime = timestamp + (7 * 24 * 60 * 60 * 1000); // 7 days in milliseconds
  const dataToSign = `${userId}:${timestamp}:${expiryTime}`;
  const signature = createHmac("sha256", SHARED_SECRET).update(dataToSign).digest("hex");
  
  // Return token with all components needed for verification
  return {
    token: `${userId}:${timestamp}:${expiryTime}:${signature}`,
    expires: new Date(expiryTime).toISOString()
  };
}

// Verify web auth token (server-side)
export async function verifyWebAuthToken(ctx) {
  const SHARED_SECRET = 'Sfd789473shjml87Chg7kd78kdc7434hggfkfxxcRET'; // Store securely!
  const authToken = ctx.req.header("x-web-auth-token");
  
  if (!authToken) {
    ctx.status(401);
    return ctx.json({ error: "Missing auth token" });
  }
  
  // Parse token components
  const [userId, timestamp, expiryTime, receivedSignature] = authToken.split(":");
  
  // Check if token has expired
  if (Date.now() > Number(expiryTime)) {
    ctx.status(401);
    return ctx.json({ error: "Token expired" });
  }
  
  // Verify signature
  const dataToSign = `${userId}:${timestamp}:${expiryTime}`;
  const expectedSignature = createHmac("sha256", SHARED_SECRET).update(dataToSign).digest("hex");
  
  // Use timingSafeEqual for secure comparison
  const signatureBuf = Buffer.from(receivedSignature);
  const expectedSigBuf = Buffer.from(expectedSignature);
  
  if (signatureBuf.length !== expectedSigBuf.length) {
    ctx.status(401);
    return ctx.json({ error: "Invalid signature length" });
  }
  
  if (!timingSafeEqual(signatureBuf, expectedSigBuf)) {
    ctx.status(401);
    return ctx.json({ error: "Invalid signature" });
  }
  
  // Token is valid
  return ctx.json({ 
    authenticated: true, 
    userId: userId,
    expires: new Date(Number(expiryTime)).toISOString()
  });
}

// Client-side example
async function login(username, password) {
  // Authenticate user with your backend
  const response = await fetch('/api/login', {
    method: 'POST',
    body: JSON.stringify({ username, password })
  });
  
  const data = await response.json();
  
  if (data.success) {
    // Store the auth token
    localStorage.setItem('authToken', data.token);
    localStorage.setItem('tokenExpires', data.expires);
  }
}

// Add auth token to requests
async function fetchProtectedResource() {
  const token = localStorage.getItem('authToken');
  
  const response = await fetch('/api/protected-resource', {
    headers: {
      'x-web-auth-token': token
    }
  });
  
  return response.json();
}