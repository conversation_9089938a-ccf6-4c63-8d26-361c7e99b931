    /**
     * use to get quotes data of symbols.
     * @param {Array} req - array of symbols.
     * @returns {Promise} returns success message on resolve or error message and error code on failure
     */
    getQuotes = async (req) => {
        var funcname = 'getQuotes'
        var logger = this.<PERSON><PERSON>
        let AuthrizationToken = this.AccessToken
        let vers = this.Version
        return new Promise(async function (resolve, reject) {
            try {
                var url = Config.data_Api1 + Config["quotes"] // "data_Api1": "https://api-t1.fyers.in/data", "quotes" : "/quotes",
                var symbolstring = ""
                req.forEach(function (element) {  // the symbol array is being read as a string
                    symbolstring = symbolstring + element + ','
                })
                symbolstring = symbolstring.slice(0, -1) //remove last ','
                var data = { "symbols": symbolstring }
                const urlParams = new URLSearchParams(data);
                url = url + '?' + urlParams
                await axios.get((url), {
                    headers: {
                        Authorization: AuthrizationToken,
                        version: vers
                    }
                })
                    .then(Response => {
                        logger.debug(`${funcname} response`, Response.data, funcname)
                        resolve(Response.data)
                    })
            }
            catch (e) {
                var err = new errorHandler(e).getError()
                logger.error(`${funcname} response`, { "Error": err, "inputs": req }, funcname)
                reject(err)
            }
        })
    }