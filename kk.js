// ts-check
import { WorkflowEntrypoint} from "cloudflare:workers";

export class TradeWorkFlow extends WorkflowEntrypoint {
  async run(event, step) {
    const kv = this.env.TRADE_KV;
    const creds = { 
        availableFunds: 100,
        margin: "20pc",
        Stock: "NSC:LT-EQ",
        Details: {
          high: 134.8,
          low: 132.9
        }
    }
    const put = await kv.put("TRADE_CREDS", JSON.stringify(creds, null, 2));
    const val = JSON.parse(await kv.get("TRADE_CREDS"));
    console.log(val);
    // Do Calculations.
    let value = await step.do("Set Number", async() => { return 1 });
    while (value <= 10) {
        value = await step.do("Set ++Number ", async() => { return ++value });  // Pre-increment operator ensures the step.do function captures the value after increment.
        await step.sleep("Run Every 2 sec", 2000);
        console.log('Now at: ', value, "2's Power: ", 2 ** value);
    }
    if (value === 11) {
    const response = await step.do("Send Message", async() => await sendToTelegram('Counter has reached 10.', this.env));
    console.log(response);
    }
  }
}

async function startWorkflow(env) {
	const instance = await env.TRADE_WORKFLOW.create();
    console.log('Started Workflow.');
    //await sendToTelegram('Started Workflow.', env);
    //await sendToTelegram( JSON.stringify(await instance.status()), env );
}

// Send message to Telegram.
async function sendToTelegram(text, env) {
    const botToken = env.TELE_BOT_TOKEN;
    const chatId = env.TELE_CHAT_ID;
    await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        chat_id: chatId,
        text: text,
      }),
   });
}

export default {
	async fetch(req, env, ctx) {
      //await sendToTelegram('Cron Triggered.', env);
	  	await startWorkflow(env);
      console.log('Started Cron Schedule.');
      return new Response('Started at ' + new Date().toLocaleString() + '.');
	}
}
