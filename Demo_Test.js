// deno run --allow-env --env-file="./.env" --allow-read --allow-write --allow-net "./Demo_Test.js"
// deno task run

// code-oss://augment.vscode-augment/auth/result?code=_e31910de1a727a42d9508019571f6dc8&state=4de23727-39be-4480-a743-dc7837b1a20d&tenant_url=https%3A%2F%2Fi0.api.augmentcode.com%2Fq  --  &state=4de23727-39be-4480-a743-dc7837b1a20d&scope=email&prompt=login
// https://auth.augmentcode.com/terms-accept?response_type=code&code_challenge=Wz-XbDkWTaTdk97Hr97KnqVIEc_o9f5aWjKormfpw0o&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=code-oss%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=4de23727-39be-4480-a743-dc7837b1a20d&scope=email&prompt=login

import { marketStatus, getQuotations, getQuotes, getTodaysAccessToken, formatTradeLog } from "./tradeExports.js";

const creds = JSON.parse(Deno.readTextFileSync("./Test_Creds.json", "utf8"));

// Strategy for Demo Trading with multiple stocks.
async function strategy() {
  const symbols = creds.Stock;
  let activeStocks = {}; // Track active trades
  let tradeLog = []; // Store completed trade logs
  let tradeCount = 0; // Serial number for trades

  async function strategyEntry() {
    try {
      // Get quotes for all symbols at once
      const quotes = await getQuotes(symbols);

      // Loop through each quote and check for breakouts
      for (let i = 0; i < quotes.length; i++) {
        const symbol = symbols[i];
        if (activeStocks[symbol]) continue; // Skip if already in a trade

        const quote = quotes[i];
        const currentPrice = quote.v.lp;
        const { high, low } = creds.Details[i];
        const quantity = Math.floor((creds.available_funds * 0.05) / currentPrice); // Use 5% of funds per trade

        if (currentPrice > high) { // Long Entry
          activeStocks[symbol] = {
            symbol,
            orderType: "LONG",
            entryPrice: currentPrice,
            quantity,
            stopLevel: Number((currentPrice * 0.990).toFixed(1)), // 1% below entry
            entryTime: new Date().toISOString(),
            details: creds.Details[i]
          };

          creds.trade_count += 1;

          // Log trade entry
          console.log(`
          🟦 DEMO TRADE ENTRY - LONG
          Entered: ${symbol}
          Price: ${currentPrice}
          Quantity: ${quantity}
          Initial Stop Loss: ${activeStocks[symbol].stopLevel}
          `);

          // Save active stocks to file
          Deno.writeTextFileSync(
            "./ActiveStocks.json",
            JSON.stringify(activeStocks, null, 2)
          );
        } else if (currentPrice < low) { // Short Entry
          activeStocks[symbol] = {
            symbol,
            orderType: "SHORT",
            entryPrice: currentPrice,
            quantity,
            stopLevel: Number((currentPrice * 1.010).toFixed(1)), // 1% above entry
            entryTime: new Date().toLocalTimeString('en-GB', { timeZone: "Asia/Kolkata" }),
            details: creds.Details[i]
          };

          creds.trade_count += 1;

          // Log trade entry
          console.log(`
          🟥 DEMO TRADE ENTRY - SHORT
          Entered: ${symbol}
          Price: ${currentPrice}
          Quantity: ${quantity}
          Initial Stop Loss: ${activeStocks[symbol].stopLevel}
          `);

          // Save active stocks to file
          Deno.writeTextFileSync(
            "./ActiveStocks.json",
            JSON.stringify(activeStocks, null, 2)
          );
        }
      }
    } catch (error) {
      console.error("⚠️ Error in strategy entry:", error.message);
    }
  }

  // Enter positions
  const entryInterval = setInterval(strategyEntry, 60000); // Check every minute

  async function strategyCheck() {
    try {
      // Use proper time zone handling
      const now = new Date();
      const ist = { timeZone: "Asia/Kolkata" };
      const istTime = now.toLocaleString("en-US", ist);
      const istDate = new Date(istTime);

      const hours = istDate.getHours();
      const minutes = istDate.getMinutes();

      // Market closing time (15:30 IST)
      const isMarketClosing = hours === 15 && minutes >= 25;

      // Get all active symbols
      const activeSymbols = Object.keys(activeStocks);
      if (activeSymbols.length === 0) return;

      // Get quotes for all active symbols
      const quotes = await getQuotes(activeSymbols);

      for (let i = 0; i < quotes.length; i++) {
        const quote = quotes[i];
        const symbol = activeSymbols[i];
        const stock = activeStocks[symbol];
        const nowPrice = quote.v.lp;

        if (
          (stock.orderType === "SHORT" && nowPrice > stock.stopLevel) ||
          (stock.orderType === "LONG" && nowPrice < stock.stopLevel) ||
          isMarketClosing
        ) {
          // Calculate P&L
          const pnl = (stock.orderType === "LONG")
            ? (nowPrice - stock.entryPrice) * stock.quantity
            : (stock.entryPrice - nowPrice) * stock.quantity;

          // Update funds
          creds.available_funds += Number(pnl.toFixed(2));
          creds.twoPctRisk += +((pnl * 2).toFixed(2));

          // Create trade log entry
          const pnlPercentage = ((pnl / (stock.entryPrice * stock.quantity)) * 100).toFixed(2);
          const pnlTag = pnl >= 0 ? "PROFIT" : "LOSS";

          tradeCount++;
          const tradeLogEntry = {
            serialNo: tradeCount,
            symbol: stock.symbol,
            orderType: stock.orderType,
            entryPrice: stock.entryPrice,
            exitPrice: nowPrice,
            quantity: stock.quantity,
            initialStopLoss: stock.stopLevel,
            pnl: pnl.toFixed(2),
            pnlPercentage: pnlPercentage,
            entryTime: stock.entryTime,
            exitTime: new Date().toISOString(),
            result: pnlTag
          };

          tradeLog.push(tradeLogEntry);

          // Save trade log to file
          Deno.writeTextFileSync(
            "./TradeLog.txt",
            formatTradeLog(tradeLog),
            { append: false }
          );

          // Log trade exit
          console.log(`
          🟨 DEMO TRADE EXIT
          ${pnlTag === "PROFIT" ? "🟢 PROFIT" : "🔴 LOSS"}
          Stock: ${symbol}
          Exit Price: ${nowPrice}
          P&L Amount: ₹ ${pnl.toFixed(2)}
          P&L %: ${+(pnlPercentage)} %
          Trade Type: ${stock.orderType}
          `);

          // Remove from active stocks
          delete activeStocks[symbol];

          // Save updated active stocks and creds
          Deno.writeTextFileSync(
            "./ActiveStocks.json",
            JSON.stringify(activeStocks, null, 2)
          );

          Deno.writeTextFileSync(
            "./Test_Creds.json",
            JSON.stringify(creds, null, 2)
          );
        } else {
          // Update trailing stop loss
          const entry = stock.entryPrice, stop = stock.stopLevel;
          const achievedGoodRR = Math.abs(nowPrice - entry) >= Math.abs(entry - stop);

          if (stock.orderType === "LONG") {
            const newStopLevel = achievedGoodRR ? nowPrice * 0.995 : nowPrice * 0.990;
            stock.stopLevel = newStopLevel > stock.stopLevel
                ? +(newStopLevel.toFixed(1))
                : +stock.stopLevel;
          } else {
            const newStopLevel = achievedGoodRR ? nowPrice * 1.005 : nowPrice * 1.010;
            stock.stopLevel = newStopLevel < stock.stopLevel
                ? +(newStopLevel.toFixed(1))
                : +stock.stopLevel;
          }

          // Save updated active stocks
          Deno.writeTextFileSync(
            "./ActiveStocks.json",
            JSON.stringify(activeStocks, null, 2)
          );
        }
      }
    } catch (error) {
      console.error("⚠️ Error in strategy check:", error.message);
    }
  }



  const checkInterval = setInterval(strategyCheck, 60000); // Check every minute

  // Clean up on script termination
  return () => {
    clearInterval(entryInterval);
    clearInterval(checkInterval);
  };
}

// The main Trading Script.
async function mainScript() {
  try {
    // Check if market is open
    const isMarketOpen = await marketStatus();
    if (!isMarketOpen) {
      console.log("🟧 Market is closed. Demo Trading will not start.");
      return;
    }

    console.log("🟪 Starting Demo Trading System...");

    // Get access token before making any API requests.
    await getTodaysAccessToken();

    // Get quotations and save selected stock.

    await getQuotations();

    // Run strategy
    await strategy();

  } catch (error) {
    console.error("🟧 Error:", error.message);
  }
}

mainScript();
