// Do not use Console Logs in production as the market is always open and number of logs will become too much.

import { 
  <PERSON><PERSON>uotes, GetRanges, SendToTelegram, GetLotSizes,
  SetMovingAverages, PlaceOrder, GetAccountBalance,
  // CreateHmac, CreateHash, Socket, GetPositions, GetOHLC
} from './FunctionExports.js'

import { CheckBreakout, CheckStoploss, CheckMACrossover, CheckMAStoploss } from './Strategies.js'

// Delta Exchange API Management Page: https://demo.delta.exchange/app/account/manageapikeys

let EnteredTrade = false, AchievedGoodRR = false, LoopCount = 0

let EnteredTrade_MA = false, AchievedGoodRR_MA = false, LoopCount_MA = 0

// const INIT_PCT_UP = 1.010, INIT_PCT_DOWN = 0.990, FINAL_PCT_UP = 1.005, FINAL_PCT_DOWN = 0.995 // Stop Loss Percentages.

async function MainScript_MA() {
  const time = new Date(Date.now())
  const day = time.getDate(), hour = time.getHours()
  // Exit the function and give the VPS a rest for 4 hours on Sundays.
  // At 10 AM on each Sunday , the Batch (.bat) file code will again activate the script which will continue till next Sunday.
  if ( day === 0 && hour === 6 ) {
    clearInterval(IntervalID_MA)
    console.log('Closing Script Execution. Exiting...')
    return
  }
  if ( !EnteredTrade_MA ) {
    await CheckMACrossover()
  }
  if ( EnteredTrade_MA ) {
    await CheckMAStoploss()
  }
}

async function MainScript() {
  const time = new Date(Date.now())
  const day = time.getDate(), hour = time.getHours()
  // Exit the function and give the VPS a rest for 4 hours on Sundays.
  // At 10 AM on each Sunday , the Batch (.bat) file code will again activate the script which will continue till next Sunday.
  if ( day === 0 && hour === 6 ) {
    clearInterval(IntervalID)
    console.log('Closing Script Execution. Exiting...')
    return
  }
  if ( !EnteredTrade ) {
    await CheckBreakout()
  }
  if ( EnteredTrade ) {
    await CheckStoploss()
  }
}

await GetAccountBalance() // Get the Available Funds for the Account before starting the script.
await GetLotSizes() // Get the Lot Sizes for different Contracts before starting the script.
await GetRanges() // Get the Price Ranges for different Contracts before starting the script.
await SetMovingAverages() // Get the Moving Averages for different Contracts before starting the script.

const IntervalID = setInterval(MainScript, 15000)
const IntervalID_MA = setInterval(MainScript_MA, 15000)

// deno task run
