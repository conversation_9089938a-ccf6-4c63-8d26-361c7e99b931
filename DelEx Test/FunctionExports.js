// Data APIs On Delta Exchange Do Not Require Authentication.
// Both Production And Demo API Endpoints Can Be Used To Fetch Data Without Auth Headers.

// Send message to Telegram.
export async function SendToTelegram(text) {
    const botToken = Deno.env.get("TELEGRAM_BOT_TOKEN");
    const chatId = Deno.env.get("TELEGRAM_CHAT_ID");
    await fetch(`https://api.telegram.org/bot${botToken}/sendMessage`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        chat_id: chatId,
        parse_mode: "HTML",
        text: text,
      }),
    });
}

export async function GetLotSizes() {
  const KV = await Deno.openKv(), KEY = ["DENO_KV", "CREDS"]
  const Creds = await KV.get(KEY)
  const Lots = {}, Contracts = [ 'BTCUSD', 'ETHUSD', 'SOLUSD', 'XRPUSD', 'ADAUSD' ]
  for (const Asset of Contracts) {
    const { result: { contract_value: Size } } = await ( await fetch('https://api.india.delta.exchange/v2/products/' + Asset) ).json()
    Lots[Asset] = Size
  }
  Creds.LotSizes = Lots
  await Deno.writeTextFileSync( './Creds.json', JSON.stringify(Creds, null, 4) )
  return Lots
}

// Get Realtime Feed From WebSocket.
export function Socket(SymbolString) { // SymbolString = 'BTCUSD,ETHUSD,SOLUSD,XRPUSD'
    const DataSocket = new WebSocket('wss://socket.india.delta.exchange')
    const json = { type: "subscribe", payload: { channels: [ { name: "v2/ticker", symbols: [ SymbolString ] } ] } }
    
    DataSocket.onopen = () => DataSocket.send(JSON.stringify(json))
  
    DataSocket.onerror = err => console.error('⚠️ Web Socket Error: ', err)

    DataSocket.onmessage = msg => { console.log('Web Socket Data: ', msg.data) }
  
    DataSocket.onclose = () => console.log('⚠️ Web socket Connection Closed.')

    return DataSocket
}

// Get Quotes/Tickers for Instruments using comma-separated Symbols.
export async function GetQuotes(symbols) {
  const response = await (await fetch('https://api.india.delta.exchange/v2/tickers/' + symbols /*'BTCUSD,ETHUSD,SOLUSD,XRPUSD'*/)).json()
  return response.result
}

// Get Historical OHLC Candles. Only One Symbol Supported Per Request.
export async function GetOHLC(queries) {
  const data = await ( await fetch('https://api.india.delta.exchange/v2/history/candles?' + queries /*'resolution=5m&symbol=BTCUSD&start=1685618835&end=1722511635'*/)).json();
  return data.result
}

export async function SetMovingAverages() {
  const KV = await Deno.openKv(), KEY = ["DENO_KV", "CREDS"]
  const Creds = await KV.get(KEY)
  const Symbols = Creds.Assets.map( (cred) => cred.Symbol )
  for (const Symbol of Symbols) {
    const PriceArray = []
    const QueryParams = {
      resolution: '15m',
      symbol: Symbol,
      start: Math.floor( (Date.now() - 20 /* 20 Periods */ * 15 /* 15 Minutes */ * 60 * 1000) / 1000 ),
      end: Math.floor( Date.now() / 1000 )
    }
    const Candles = await GetOHLC( new URLSearchParams(QueryParams) )
    for (const Candle of Candles) PriceArray.push(Candle.close)
    Creds.ClosingPrices[Symbol] = PriceArray
    const MA_9 = Creds.MovingAverages[Symbol].MA_9 = +( (PriceArray.slice(0, 9).reduce((a, b) => a + b, 0) / 9).toFixed(4) )
    const MA_20 = Creds.MovingAverages[Symbol].MA_20 = +( (PriceArray.slice(0,20).reduce((a, b) => a + b, 0) / 20).toFixed(4) )
    Creds.MA_9_Positions[Symbol] = MA_9 > MA_20 ? 'Above' : 'Below'
  }
  KV.set(KEY, Creds)
  // Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) )
}

export async function GetMovingAverage({ Symbol, Period, Timeframe }) { // { 'BTCUSD', 9, '1h' } -> Moving Average Of Last 9 Hourly Candles.
  const Multiplier = { 
    '1m': 1, '3m': 3, '5m': 5, '15m': 15, '30m': 30, '1h': 60, '2h': 120, '4h': 240, '6h': 360,
    '1d': 1440, '7d': 10080, '30d': 43200, '1w': 100800, '2w': 201600
  } // Values Are In Minutes.

  const queryParams = { 
      resolution: Timeframe, // Supported Resolutions: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 1d, 7d, 30d, 1w, 2w.
      symbol: Symbol,
      start: Math.floor( (Date.now() - Period * Multiplier[Timeframe] * 60 * 1000) / 1000 ),
      end: Math.floor( Date.now() / 1000 )
  }
  const candles = await GetOHLC( new URLSearchParams(queryParams) )
  let sum = 0
  for (const candle of candles) {
      sum += candle.close
  }
  return sum / candles.length
}

// Find Top 5 Coins with small High-Low Ranges.
export async function GetRanges() {
  const Contracts = [ 'BTCUSD', 'ETHUSD', 'SOLUSD', 'XRPUSD', 'ADAUSD' ]
  const highLowArray = []
  const KV = await Deno.openKv(), KEY = ["DENO_KV", "CREDS"]
  const Creds = await KV.get(KEY)
  for (const symbol of Contracts) {
    const queryParams = { 
      resolution: '5m',  // Supported Resolutions: 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 1d, 7d, 30d, 1w, 2w.
      symbol,
      start: Math.floor( (Date.now() - 60 * 60 * 1000) / 1000 ),  // 1 Hour Before Now.
      end: Math.floor( Date.now() / 1000 )
    }
    const candles = await GetOHLC( new URLSearchParams(queryParams) )
    let High, Low, RangePct, hlRange = []
    candles.forEach((candle) => {
      const { high, low } = candle
      hlRange.push(high, low)
    })
    hlRange.sort((a, b) => a - b)
    High = hlRange[hlRange.length - 1]
    Low = hlRange[0]
    RangePct = +(Math.abs((High - Low) / Low * 100).toFixed(3))
    highLowArray.push( { Symbol: symbol, Details: { RangePct, High, Low } } ) // The keys will be the notional strings and the values will be their respective references.
  }
  const SortedArray = highLowArray.sort((a, b) => a.Details.RangePct - b.Details.RangePct)
  Creds.Assets = SortedArray // .slice(0,5);
  KV.set(KEY, Creds)
  // Deno.writeTextFileSync( "./Creds.json", JSON.stringify(Creds, null, 4) );
  highLowArray.length = 0
}

export async function PlaceOrder({ Symbol, Quantity, TradeType, SquareOff_Only }) {
  const url = 'https://cdn-ind.testnet.deltaex.org/v2/orders'
  const timestamp = Date.now().toString().slice(0, 10)
  const payload = JSON.stringify({
    product_symbol: Symbol,
    size: Quantity,
    side: TradeType,
    reduce_only: SquareOff_Only, // Boolean. If "true", it only squares off existing positions and does not create new positions.
    order_type: "market_order",
    // client_order_id: "12345"
  })
  console.log('Payload: ', payload)
  const message = 'POST' + timestamp + '/v2/orders' + '' + payload // Format: method + timestamp + path + queryString + payload
  const signature = await GenerateSignature(message)
  const header = {
    'api-key': Deno.env.get('DELTA_API_KEY'),
    'timestamp': timestamp,
    'signature': signature,
    'User-Agent': 'js-rest-client',
    'Content-Type': 'application/json'
  }

  const response = await fetch(url, {
    method: 'POST',
    headers: header,
    body: payload
  })
  const data = await response.json()
  console.log(data)
  return data
}

export async function GetPositions(Symbol) {
  const url = 'https://cdn-ind.testnet.deltaex.org'
  const path = '/v2/positions?underlying_asset_symbol=' + Symbol // Creds.TradedAsset.slice(0, 3)
  const timestamp = Date.now().toString().slice(0, 10) // Math.floor(Date.now() / 1000).toString()
  const message = 'GET' + timestamp + path + '' + '' // Format: method + timestamp + path + queryString + payload
  const signature = await GenerateSignature(message)
  const header = {
    'api-key': Deno.env.get('DELTA_API_KEY'),
    'timestamp': timestamp,
    'signature': signature,
    'User-Agent': 'js-rest-client',
    'Content-Type': 'application/json'
  }
  const data = await ( await fetch(url + path, { method: 'GET', headers: header }) ).json()
  console.log(data)
  return data
}

export async function SyncCreds() {
  const KV = await Deno.openKv(), Creds = await KV.get( ["DENO_KV", "CREDS"] )
  await Deno.writeTextFileSync( './Creds.json', JSON.stringify(Creds, null, 4) )
}

export async function GetAccountBalance() {
  const KV = await Deno.openKv(), KEY = ["DENO_KV", "CREDS"]
  const Creds = await KV.get(KEY)
  const url = 'https://cdn-ind.testnet.deltaex.org/v2/wallet/balances'
  const timestamp = Date.now().toString().slice(0, 10)
  const message = 'GET' + timestamp + '/v2/wallet/balances' + '' + '' // Format: method + timestamp + path + queryString + payload
  const signature = await GenerateSignature(message)
  const header = {
    'api-key': Deno.env.get('DELTA_API_KEY'),
    'timestamp': timestamp,
    'signature': signature,
    'User-Agent': 'js-rest-client',
    'Content-Type': 'application/json'
  }
  const data = await ( await fetch(url, { method: 'GET', headers: header }) ).json()
  console.log('Response From Delta Exchange: ', data)
  const Balance = data.meta.net_equity
  Creds.Balance = Balance
  KV.set(KEY, Creds)
  return Balance
}

// Create Hash using Web Crypto API.
export async function CreateHash(text) {
    const dataBytes = new TextEncoder().encode(text);
    const hash = await crypto.subtle.digest("SHA-256", dataBytes);
    const hashArray = Array.from(new Uint8Array(hash));
    const hashHex = hashArray.map(b => b.toString(16).padStart(2, "0")).join('');
    return hashHex;
}

// Create HMAC using Web Crypto API.
export async function CreateHmac(text) {
    const secret = Deno.env.get("SHARED_SECRET_STRING");
    // Import the key.
    const keyBytes = new TextEncoder().encode(secret);
    const cryptoKey = await crypto.subtle.importKey(
      "raw", // Raw format for the key data.
      keyBytes,
      { name: "HMAC", hash: "SHA-256" },
      false, // Not extractable.
      ["sign"] // For signing only.
    );
    // Then sign the data.
    const dataBytes = new TextEncoder().encode(text);
    const hmacBuffer = await crypto.subtle.sign( { name: "HMAC" }, cryptoKey, dataBytes );
    // Convert to hex string.
    const hmacArray = Array.from(new Uint8Array(hmacBuffer));
    const hmacHex = hmacArray.map(b => b.toString(16).padStart(2, "0")).join('');
    return hmacHex;
}


// Web Crypto-based HMAC-SHA256 signature function.
const baseUrl = 'https://api.india.delta.exchange';
const apiKey = 'a207900b7693435a8fa9230a38195d';
const apiSecret = '7b6f39dcf660ec1c7c664f612c60410a2bd0c258416b498bf0311f94228f';

async function GenerateSignature(message) {
  const encoder = new TextEncoder();
  const keyData = encoder.encode(Deno.env.get('DELTA_API_SECRET'));
  const msgBuffer = encoder.encode(message);

  const cryptoKey = await crypto.subtle.importKey(
    'raw',
    keyData,
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );

  const signature = await crypto.subtle.sign('HMAC', cryptoKey, msgBuffer);
  const signedHex = Array.from(new Uint8Array(signature)).map(b => b.toString(16).padStart(2, '0')).join('');
  return signedHex;
}

async function GetOpenOrders({ method, path, queryString, payload }) {
  // const method = 'GET';
  const timestamp = Math.floor(Date.now() / 1000).toString() // Timestamp is valid for 5 seconds only until reaching Delta Server.8
  // const path = '/v2/orders';
  // const queryString = '?product_id=1&state=open';

  const signatureData = method + timestamp + path + queryString + payload // Format: method + timestamp + path + queryString + payload
  const signature = await GenerateSignature(signatureData);

  const headers = {
    'api-key': Deno.env.get('DELTA_API_KEY'),
    'timestamp': timestamp,
    'signature': signature,
    'User-Agent': 'js-rest-client',
    'Content-Type': 'application/json'
  };

  const url = `${baseUrl}${path}${queryString}`;

  const response = await fetch(url, {
    method,
    headers
  });

  const data = await response.json();
  console.log('Open Orders:', data);
}