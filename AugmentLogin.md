# Augment next Edit Keyboard Shortcuts

Go to next -> Ctrl + ;
Go to previous -> Ctrl + Shift + ;
Accept suggestion -> Enter
Reject suggestion -> Backspace

# Augment Code Authentication Guide

## Downloading the Augment Code Extension (.vsix file)

Before authenticating, you need to download the Augment Code extension. The extension is available as a .vsix file that can be installed in VS Code or other compatible editors.

### Direct Download Method

You can download the extension directly using the VS Code Marketplace API URL format:

```
https://${publisher}.gallery.vsassets.io/_apis/public/gallery/publisher/${publisher}/extension/${extension name}/${version}/assetbyname/Microsoft.VisualStudio.Services.VSIXPackage
```

### Augment Code Extension Download Link

For the Augment Code extension specifically, use this URL:

```
https://augment.gallery.vsassets.io/_apis/public/gallery/publisher/augment/extension/vscode-augment/0.434.0/assetbyname/Microsoft.VisualStudio.Services.VSIXPackage
```

> **Note:** This link downloads version 0.434.0 of the extension. Check for newer versions if available.

### Installation Instructions

1. Download the .vsix file using the link above
2. In VS Code, go to the Extensions view (Ctrl+Shift+X)
3. Click on the "..." menu (More Actions) at the top of the Extensions view
4. Select "Install from VSIX..."
5. Navigate to and select the downloaded .vsix file
6. Restart VS Code when prompted

For Firebase Studio users, add the downloaded .vsix file to your Firebase Studio extensions.

## Authentication Overview
This document provides step-by-step instructions for completing the authentication process for Augment Code's VS Code extension. The authentication uses an OAuth flow that requires copying parameters between URLs.

## Authentication Process Overview

### Step 1: Initial Authentication URL
```
https://auth.augmentcode.com/terms-accept?response_type=code&code_challenge=9SoymLLb-WRaxAJhmxZ70JfIJOBXiPQYCxWOPwVgGtU&code_challenge_method=S256&client_id=augment-vscode-extension&redirect_uri=code-oss%3A%2F%2Faugment.vscode-augment%2Fauth%2Fresult&state=f8642378-f775-484c-b2e8-5780220410af&scope=email&prompt=login
```

> **Note:** This URL is opened in your browser when you initiate the authentication process. It contains important parameters including the OAuth state, scope, and code challenge that are part of the security flow.

### Step 2: Extract Parameters
From the URL above, you need to extract the following parameters:
```
&state=f8642378-f775-484c-b2e8-5780220410af&scope=email&prompt=login
```

> **Important:** These parameters must be copied exactly as they appear in the original URL to maintain the security of the authentication process.

### Step 3: Modify the Callback URL
The VS Code extension will generate a callback URL that looks like this:
```
code-oss://augment.vscode-augment/auth/result?code=_c72c7bd6f1f157a905911360f9037b46&state=f8642378-f775-484c-b2e8-5780220410af&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F
```

You need to append the parameters from Step 2 to this URL:
```
code-oss://augment.vscode-augment/auth/result?code=_c72c7bd6f1f157a905911360f9037b46&state=f8642378-f775-484c-b2e8-5780220410af&tenant_url=https%3A%2F%2Fd13.api.augmentcode.com%2F&state=f8642378-f775-484c-b2e8-5780220410af&scope=email&prompt=login
```

> **Technical Detail:** The callback URL contains an authorization code and tenant information that, when combined with the state parameter, allows the extension to securely complete the OAuth flow.

### Step 4: Open the Modified URL in VS Code
1. Press `Ctrl + Shift + P` to open the command palette
2. Type "Open URL" and select the command
3. Paste the modified URL from Step 3
4. Press Enter

> **Result:** This action will pass the authentication information to the VS Code extension, completing the login process.

## Troubleshooting
- If authentication fails, ensure you've copied the parameters exactly
- Check that you're using the most recent callback URL generated by the extension
- Verify that your internet connection is stable throughout the process

## Security Notes
- Never share these URLs with others as they contain sensitive authentication tokens
- The authentication process uses PKCE (Proof Key for Code Exchange) for enhanced security
- The state parameter helps prevent cross-site request forgery attacks
