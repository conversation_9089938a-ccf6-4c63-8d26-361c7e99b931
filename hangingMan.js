// Hanging Man candlestick strategy by <PERSON><PERSON><PERSON><PERSON> Tech quantified by Gemini 2.5 Pro.
/**
 * Identifies the Hanging Man bearish reversal pattern and generates signals.
 * Based on rules described by Ghanshyam Tech / Art of Trading.
 *
 * @param {Array<object>} ohlcvData Array of OHLCV objects {timestamp, open, high, low, close, volume}
 * @param {object} params Strategy parameters
 * @param {number} params.lookbackPeriod=10 Number of candles to check for prior uptrend.
 * @param {number} params.trendThresholdPercent=1.0 Minimum percentage rise for uptrend confirmation.
 * @param {number} params.bodyMaxRangePercent=25 Maximum body size relative to candle range (%).
 * @param {number} params.minLowerShadowRatio=2 Minimum ratio of lower shadow size to body size.
 * @param {number} params.maxUpperShadowRatio=1 Maximum ratio of upper shadow size to body size.
 * @param {number} params.confirmationLookback=3 Max number of candles to wait for confirmation.
 * @param {number} params.stopLossBufferPercent=0.1 Percentage buffer above high for stop loss.
 * @param {number} params.riskRewardRatio=1.5 Target price based on risk-reward ratio.
 * @returns {Array<object>} Array of signal objects {timestamp, type, entryTrigger, stopLoss, target}
 */
function hangingManStrategy(ohlcvData, params = {}) {
    const {
        lookbackPeriod = 10,
        trendThresholdPercent = 1.0, // Default 1% for general use, adjust for specific assets
        bodyMaxRangePercent = 25,
        minLowerShadowRatio = 2,
        maxUpperShadowRatio = 1,
        confirmationLookback = 3,
        stopLossBufferPercent = 0.1,
        riskRewardRatio = 1.5
    } = params;

    const signals = [];
    if (!ohlcvData || ohlcvData.length <= lookbackPeriod + confirmationLookback) {
        console.warn("Not enough data for strategy execution.");
        return signals;
    }

    for (let i = lookbackPeriod + 1; i < ohlcvData.length - confirmationLookback; i++) {
        // --- 1. Identify Potential Hanging Man (Candle 'i') ---
        const hmCandle = ohlcvData[i];
        const bodySize = Math.abs(hmCandle.open - hmCandle.close);
        const candleRange = hmCandle.high - hmCandle.low;
        const lowerShadow = Math.min(hmCandle.open, hmCandle.close) - hmCandle.low;
        const upperShadow = hmCandle.high - Math.max(hmCandle.open, hmCandle.close);

        // Basic validity checks
        if (candleRange === 0 || bodySize === 0) continue; // Avoid division by zero or Dojis as HM

        const isSmallBody = (bodySize / candleRange) * 100 <= bodyMaxRangePercent;
        const hasLongLowerShadow = lowerShadow >= bodySize * minLowerShadowRatio;
        const hasSmallUpperShadow = upperShadow <= bodySize * maxUpperShadowRatio;

        const isPotentialHM = isSmallBody && hasLongLowerShadow && hasSmallUpperShadow;

        if (!isPotentialHM) continue;

        // --- 2. Check Prior Uptrend ---
        let lowestLowInLookback = Infinity;
        let highestHighInLookback = -Infinity;
        for (let j = i - lookbackPeriod; j < i; j++) {
            if (ohlcvData[j].low < lowestLowInLookback) {
                lowestLowInLookback = ohlcvData[j].low;
            }
             if (ohlcvData[j].high > highestHighInLookback) {
                highestHighInLookback = ohlcvData[j].high;
            }
        }

        // Check if HM candle is near the peak of the lookback period
        const isAtPeak = hmCandle.high >= highestHighInLookback; // HM high should be at or very near the lookback high

        const trendRisePercent = ((hmCandle.high - lowestLowInLookback) / lowestLowInLookback) * 100;
        const isInUptrend = trendRisePercent >= trendThresholdPercent && isAtPeak;

        if (!isInUptrend) continue;

        // --- 3. Check Confirmation (Candles i+1 to i+confirmationLookback) ---
        let confirmed = false;
        let confirmationIndex = -1;
        for (let k = 1; k <= confirmationLookback; k++) {
             if (i + k >= ohlcvData.length) break; // Ensure we don't go out of bounds

            const confirmationCandle = ohlcvData[i + k];
            if (confirmationCandle.low < hmCandle.low) {
                confirmed = true;
                confirmationIndex = i + k;
                break; // Confirmed
            }
             // Optional: Add a condition to invalidate if price goes above HM high before confirmation
             if (confirmationCandle.high > hmCandle.high) {
                 break; // Invalidate if SL would have hit first
             }
        }

        // --- 4. Generate Signal ---
        if (confirmed) {
            const entryTriggerPrice = hmCandle.low; // Signal triggers when this level is broken
            const stopLossPrice = hmCandle.high * (1 + stopLossBufferPercent / 100);
            const riskAmount = stopLossPrice - entryTriggerPrice;
            const targetPrice = entryTriggerPrice - (riskAmount * riskRewardRatio);

            signals.push({
                timestamp: ohlcvData[confirmationIndex].timestamp, // Timestamp of confirmation candle
                hmTimestamp: hmCandle.timestamp, // Timestamp of HM candle
                type: 'SELL',
                entryTrigger: entryTriggerPrice,
                stopLoss: stopLossPrice.toFixed(2), // Format for clarity
                target: targetPrice.toFixed(2),   // Format for clarity
                hmCandleIndex: i,
                confirmationCandleIndex: confirmationIndex,
            });

            // Skip ahead to avoid multiple signals from the same setup
            // You might adjust this logic depending on how you want to handle overlapping signals
             i = confirmationIndex;
        }
    }

    return signals;
}

// --- Example Usage ---

// Sample OHLCV Data (replace with your actual data)
const sampleData = [
    { timestamp: '2023-10-01 09:15', open: 100, high: 101, low: 99.5, close: 100.5, volume: 1000 },
    { timestamp: '2023-10-01 09:20', open: 100.5, high: 102, low: 100.2, close: 101.8, volume: 1200 },
    { timestamp: '2023-10-01 09:25', open: 101.8, high: 103, low: 101.5, close: 102.5, volume: 1100 },
    { timestamp: '2023-10-01 09:30', open: 102.5, high: 104, low: 102.2, close: 103.8, volume: 1500 },
    { timestamp: '2023-10-01 09:35', open: 103.8, high: 105, low: 103.5, close: 104.5, volume: 1400 },
    { timestamp: '2023-10-01 09:40', open: 104.5, high: 106, low: 104.2, close: 105.8, volume: 1600 },
    { timestamp: '2023-10-01 09:45', open: 105.8, high: 107, low: 105.5, close: 106.5, volume: 1300 },
    { timestamp: '2023-10-01 09:50', open: 106.5, high: 108, low: 106.2, close: 107.8, volume: 1700 },
    { timestamp: '2023-10-01 09:55', open: 107.8, high: 109, low: 107.5, close: 108.5, volume: 1500 },
    { timestamp: '2023-10-01 10:00', open: 108.5, high: 110, low: 108.2, close: 109.8, volume: 1800 }, // Potential end of trend before HM
    // Hanging Man Candidate
    { timestamp: '2023-10-01 10:05', open: 109.7, high: 110.5, low: 107.5, close: 109.5, volume: 2000 },
    // Confirmation Candle
    { timestamp: '2023-10-01 10:10', open: 109.4, high: 109.6, low: 107.2, close: 107.8, volume: 2200 }, // Low (107.2) < HM Low (107.5) -> Confirmed
    { timestamp: '2023-10-01 10:15', open: 107.8, high: 108.5, low: 107.0, close: 107.2, volume: 1900 },
    { timestamp: '2023-10-01 10:20', open: 107.2, high: 107.5, low: 106.0, close: 106.2, volume: 2100 },
];

// Parameters for a stock-like scenario
const stockParams = {
    lookbackPeriod: 8, // Slightly shorter for potentially faster stock trends
    trendThresholdPercent: 1.5, // As mentioned for stocks
    bodyMaxRangePercent: 30, // Allowing slightly larger bodies
    minLowerShadowRatio: 2.0,
    maxUpperShadowRatio: 0.8, // Looser upper shadow rule
    confirmationLookback: 3,
    stopLossBufferPercent: 0.2, // 0.2% buffer
    riskRewardRatio: 2.0 // Aiming for 1:2 RR
};

const signals = hangingManStrategy(sampleData, stockParams);

console.log("Generated Signals:", signals);

/*
Example Output for the sample data:
Generated Signals: [
  {
    timestamp: '2023-10-01 10:10',
    hmTimestamp: '2023-10-01 10:05',
    type: 'SELL',
    entryTrigger: 107.5,
    stopLoss: '110.72', // 110.5 * (1 + 0.002)
    target: '101.06', // 107.5 - (110.72 - 107.5) * 2.0
    hmCandleIndex: 10,
    confirmationCandleIndex: 11
  }
]
*/